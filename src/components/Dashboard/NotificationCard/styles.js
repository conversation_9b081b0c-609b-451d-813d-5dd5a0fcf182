import customFont, {
  normalize,
} from '../../../constants/theme/variables/customFont';
import colors from '../../../constants/theme/variables/customColor';
import { Dimensions } from 'react-native';
const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default {
  notificationCardContainer: {
    width: SCREEN_WIDTH * 0.77,
    height: normalize(185),
    padding: normalize(16),
    backgroundColor: colors.notificationBackground,
    borderRadius: normalize(12),
    marginLeft: normalize(20),
  },
  notificationIconContainer: {
    alignItems: 'flex-start',
    paddingBottom: normalize(16),
  },
  notificationBellCircle: {
    width: normalize(32),
    height: normalize(32),
    borderRadius: normalize(16),
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(14),
    letterSpacing: 0.2,
    color: colors.actionHeadingColor,
    lineHeight: normalize(18),
    marginBottom: normalize(5),
  },
  notificationDescription: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(12),
    letterSpacing: 0.2,
    color: colors.actionHeadingColor,
    lineHeight: normalize(17),
  },
  notificationBottomContainer: {
    flexDirection: 'row',
    marginTop: normalize(0),
    position: 'absolute',
    bottom: normalize(16),
    left: normalize(16),
  },
  notificationButtonContainer: {
    width: normalize(90),
    height: normalize(25),
    borderRadius: normalize(4),
    backgroundColor: colors.secondary2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationButtonText: {
    fontFamily: customFont.RobotoBold,
    color: colors.white,
    fontSize: normalize(11),
    letterSpacing: 0.2,
  },
  notificationTimeContainer: {
    flexDirection: 'row',
    width: normalize(100),
    height: normalize(25),
    borderRadius: normalize(4),
    backgroundColor: colors.actionTimeBackgroundColor,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: normalize(8),
  },
  notificationTimeText: {
    fontFamily: customFont.RobotoMedium,
    color: colors.screenBackgroundColor3,
    fontSize: normalize(11),
    letterSpacing: 0.2,
    marginLeft: normalize(3),
  },
};
