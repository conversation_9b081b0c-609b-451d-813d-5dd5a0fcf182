// modules
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

//styles
import styles from './styles';
import { normalize } from '../../../constants/theme/variables/customFont';

//constants
import {
  NOTIFICATION_BELL,
  CALENDAR_ICON,
} from '../../../constants/AssetSVGConstants';
import colors from '../../../constants/theme/variables/customColor';

export const NotificationCard = props => {
  const { onPress = () => {} } = props;
  return (
    <TouchableOpacity
      style={styles.notificationCardContainer}
      onPress={onPress}
      activeOpacity={0.8}>
      <View style={styles.notificationIconContainer}>
        <View style={styles.notificationBellCircle}>
          <NOTIFICATION_BELL
            width={normalize(27)}
            height={normalize(27)}
            style={{ color: colors.primary }}
          />
        </View>
      </View>

      <View style={styles.notificationContent}>
        <Text
          numberOfLines={1}
          style={[styles.notificationTitle]}
          ellipsizeMode="tail">
          Notification Title
        </Text>
        <Text numberOfLines={2} style={[styles.notificationDescription]}>
          Arcu elementum semper neque arcu imperd viverra quis mauris. Arcu
          elementum semper...
        </Text>
      </View>

      <View style={styles.notificationBottomContainer}>
        <View style={styles.notificationButtonContainer}>
          <Text style={styles.notificationButtonText}>NOTIFICATION</Text>
        </View>

        <View style={styles.notificationTimeContainer}>
          <CALENDAR_ICON
            width={normalize(12)}
            height={normalize(12)}
            style={{ color: colors.screenBackgroundColor3 }}
          />
          <Text style={styles.notificationTimeText}>April/May</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default NotificationCard;
