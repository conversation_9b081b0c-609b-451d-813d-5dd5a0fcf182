// modules
import React, { useState } from 'react';
import { View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import Carousel from 'react-native-reanimated-carousel';

// components
import SectionLabel from '../SectionLabel';
import { ActionCard, EmptyActionCard, NotificationCard } from '../ActionCard';

// localization
import i18n from '../../../localization/i18n';

// actions
import { getNotebookActionTypeRequest } from '../../../store/actions/notebook';

// models
import { noteBookFilterModel } from '../../../models/noteBook';

// constants
import { NOTEBOOK_TYPE_ENUMS_CATEGORY } from '../../../constants/AppConstants';
import ROUTE_CONSTANTS from '../../../constants/RouteConstants';

// styles
import styles from './styles';

const NoteBookActionsSection = () => {
  const dispatch = useDispatch();

  const { navigate } = useNavigation();

  const notebookActionItems = useSelector(
    state => state.dashboard.notebookActionItems,
  );

  const [pageIndex, setPageIndex] = useState(0);

  // dispatch filter action for notebook and navigate
  const onViewAllActionsPress = () => {
    dispatch(
      getNotebookActionTypeRequest(
        noteBookFilterModel(
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          [NOTEBOOK_TYPE_ENUMS_CATEGORY.ACTION.noteBookType],
        ),
      ),
    );
  };

  //   navigate to NoteBook editor screen
  const navigateToEditor = item => {
    navigate?.(ROUTE_CONSTANTS.NOTE_BOOK_EDITOR, {
      siteId: item?.siteId,
      localSiteId: item?.localSiteId,
      accountId: item?.accountId,
      localAccountId: item?.localAccountId,
      visitId: item?.visitId,
      localVisitId: item?.localVisitId,
      sectionId: '',
      section: item?.section,
      sectionTitle: item?.sectionTitle,
      noteId: item?.id,
      localId: item?.localId,
      localNoteId: item?.localId,
      reminderDateTime: item?.actionNotificationDateTimeUtc,
      category: NOTEBOOK_TYPE_ENUMS_CATEGORY.ACTION,
    });
  };

  // Create combined data array with notification card
  const carouselData = React.useMemo(() => {
    const data = [...(notebookActionItems || [])];
    // Add notification card as the last item
    data.push({ type: 'notification', id: 'notification-card' });
    return data;
  }, [notebookActionItems]);

  const _renderCarouselItem = ({ item, index }) => {
    if (item.type === 'notification') {
      return (
        <NotificationCard onPress={() => console.log('Notification pressed')} />
      );
    }
    return (
      <ActionCard
        item={item}
        index={index}
        onPress={() => navigateToEditor(item)}
      />
    );
  };

  return (
    <>
      <SectionLabel
        title={i18n.t('upcomingActions')}
        onViewAllPress={onViewAllActionsPress}
      />

      <View style={styles.actionCardCarouselContainer}>
        {carouselData && carouselData.length > 0 ? (
          <Carousel
            loop={false}
            data={carouselData}
            pagingEnabled={true}
            width={styles.carouselWidth}
            height={styles.carouselHeight}
            style={styles.actionCardCarouselStyle}
            onSnapToItem={index => setPageIndex(index)}
            renderItem={_renderCarouselItem}
            scrollAnimationDuration={100}
          />
        ) : (
          <EmptyActionCard />
        )}
      </View>

      {carouselData && carouselData.length > 0 && (
        <View style={styles.carouselPaginationContainer}>
          {carouselData.map((_, index) =>
            pageIndex === index ? (
              <View
                key={'dot_' + index}
                style={styles.carouselSelectedPaginationDot}
              />
            ) : (
              <View
                key={'dot_' + index}
                style={styles.carouselUnselectedPaginationDot}
              />
            ),
          )}
        </View>
      )}
    </>
  );
};

export default NoteBookActionsSection;
