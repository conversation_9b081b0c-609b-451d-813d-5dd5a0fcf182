// modules
import { useRef, useState } from 'react';
import { View, Text, Platform, Keyboard } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// localization
import i18n from '../../../../../../localization/i18n';

// components
import NumberFormInput from '../../../../../common/NumberFormInput';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// constants
import {
  CONTENT_TYPE,
  KEYBOARD_TYPE,
} from '../../../../../../constants/FormConstants';
import {
  NEXT_FIELD_TEXT,
  UNIT_OF_MEASURE,
} from '../../../../../../constants/AppConstants';

// helpers
import {
  onValueChange,
  getCalculateMilkLoss,
  onUpdateSiteObj,
} from '../../../../../../helpers/locomotionHelper';
import {
  convertWeightToImperial,
  convertWeightToMetric,
  getWeightUnitByMeasure,
} from '../../../../../../helpers/appSettingsHelper';
import {
  convertStringToNumber,
  removeStringCommas,
} from '../../../../../../helpers/alphaNumericHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

// styles
import styles from './styles';

// actions
import { updateToolSiteRequest } from '../../../../../../store/actions/site';
import { updateLocomotionHerdAnalysisData } from '../../../../../../store/actions/tools/locomotionScore';

const HerdSiteForm = ({
  herdData,
  setIsDirty,
  isEditable,
  loadingHerdData,
  totalAnimalsRef,
}) => {
  const dimRef = useRef();
  const milkProductionRef = useRef();

  const dispatch = useDispatch();

  const siteState = useSelector(state => state.site.visitSite);
  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const onBlurInput = (newValue, oldValue) => {
    if (newValue != oldValue) {
      onSiteUpdate();
    }
  };

  const onSiteUpdate = () => {
    const updateSiteObj = onUpdateSiteObj(
      siteState,
      unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
        ? convertWeightToMetric(
            convertStringToNumber(herdData?.milkProductionInKg),
          )
        : convertStringToNumber(herdData?.milkProductionInKg),
      herdData?.daysInMilk,
      herdData?.totalAnimalsInHerd,
    );
    dispatch(updateToolSiteRequest(updateSiteObj));
  };

  // user manually change animal in per pen form pen setup calculate run time pens% and animal in per pen
  const onChangeAnimalInHerd = value => {
    value = removeStringCommas(value);
    if (onValueChange(value, 0, 99999, true, 0)) {
      const updatedData = {
        ...herdData,
        totalAnimalsInHerd: value,
      };
      dispatch(updateLocomotionHerdAnalysisData(updatedData));
      setIsDirty(true);
    }
  };

  const onChangeDaysInMilk = value => {
    if (onValueChange(value, -100, 999, true, 0, true)) {
      const updatedData = {
        ...herdData,
        daysInMilk: value,
      };
      dispatch(updateLocomotionHerdAnalysisData(updatedData));
      setIsDirty(true);
    }
  };

  const onChangeMilkProduction = value => {
    if (onValueChange(value, 0, 999, false, 1)) {
      const updatedData = {
        ...herdData,
        milkProductionInKg: value,
      };
      dispatch(updateLocomotionHerdAnalysisData(updatedData));
      setIsDirty(true);
    }
  };

  if (loadingHerdData) {
    return null;
  }

  if (!herdData || !herdData?.categories || herdData?.categories?.length <= 0) {
    return null;
  }

  return (
    <View style={styles.penSetupContainer}>
      <CustomInputAccessoryView doneAction={action} type={type} />

      <View style={styles.penSetupView}>
        <Text style={styles.penSetupText}>{i18n.t('fromSiteSetup')}</Text>
      </View>

      <View style={styles.penSetupChildContainer}>
        <View style={styles.penViewChildContainer}>
          <View style={styles.justifyCenter}>
            <Text style={styles.penChildText}>{i18n.t('animalInHerd')}</Text>
          </View>
          <NumberFormInput
            disabled={!isEditable}
            keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
            placeholder={'0'}
            value={herdData?.totalAnimalsInHerd}
            onBlur={() =>
              onBlurInput(
                herdData?.totalAnimalsInHerd,
                siteState?.lactatingAnimal,
              )
            }
            hasCommas={true}
            blurOnSubmit={false}
            onChange={onChangeAnimalInHerd}
            style={styles.input}
            reference={input => (totalAnimalsRef.current = input)}
            onSubmitEditing={() => dimRef?.current?.focus?.()}
            inputAccessoryViewID="customInputAccessoryView"
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            onFocus={() => {
              setType(CONTENT_TYPE.NUMBER);
              setAction({
                currentRef: dimRef?.current,
              });
            }}
          />
        </View>

        <View style={styles.penViewChildContainer}>
          <View style={styles.justifyCenter}>
            <Text style={styles.penChildText}>{i18n.t('daysInMilk')}</Text>
          </View>
          <NumberFormInput
            disabled={!isEditable}
            keyboardType={
              Platform.OS === 'ios'
                ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
                : KEYBOARD_TYPE.NUMBER_PAD
            }
            placeholder="0"
            blurOnSubmit={false}
            isNegative={true}
            value={herdData?.daysInMilk}
            onBlur={() =>
              onBlurInput(herdData?.daysInMilk, siteState?.daysInMilk)
            }
            hasCommas={true}
            onChange={onChangeDaysInMilk}
            style={styles.input}
            reference={input => (dimRef.current = input)}
            onSubmitEditing={() => milkProductionRef?.current?.focus?.()}
            inputAccessoryViewID="customInputAccessoryView"
            returnKeyType={NEXT_FIELD_TEXT.NEXT}
            onFocus={() => {
              setType(CONTENT_TYPE.NUMBER);
              setAction({
                currentRef: milkProductionRef.current,
              });
            }}
          />
        </View>

        <View style={styles.penViewChildContainer}>
          <View style={styles.justifyCenter}>
            <Text style={styles.penChildText}>
              {`${i18n.t('milkProduction')} (${weightUnit})`}
            </Text>
          </View>
          <NumberFormInput
            disabled={!isEditable}
            keyboardType={KEYBOARD_TYPE.DECIMAL}
            placeholder={i18n.t('singleDecimalNumberPlaceholder')}
            blurOnSubmit={false}
            onChange={onChangeMilkProduction}
            onBlur={() =>
              onBlurInput(
                herdData?.milkProductionInKg,
                unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
                  ? convertWeightToImperial(siteState?.milk, 1)
                  : siteState?.milk,
              )
            }
            value={herdData?.milkProductionInKg}
            style={styles.input}
            reference={input => (milkProductionRef.current = input)}
            onSubmitEditing={() => Keyboard?.dismiss()}
            inputAccessoryViewID="customInputAccessoryView"
            returnKeyType={NEXT_FIELD_TEXT.DONE}
            onFocus={() => {
              setType(CONTENT_TYPE.NUMBER);
              setAction({
                dismiss: true,
              });
            }}
          />
        </View>

        <View style={styles.penViewChildContainer}>
          <View style={styles.milkLossViewHeader}>
            <Text style={styles.penChildText}>{i18n.t('milkLoss')} </Text>
            <Text style={styles.penChildText}>({weightUnit})</Text>
          </View>
          <View style={styles.milkLossView}>
            <Text style={styles.milkLossText}>
              {convertInputNumbersToRegionalBasis(
                getCalculateMilkLoss(
                  herdData,
                  herdData?.milkProductionInKg,
                  true,
                ),
                4,
              )}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default HerdSiteForm;
