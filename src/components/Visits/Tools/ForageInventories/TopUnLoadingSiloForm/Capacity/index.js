// modules
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Keyboard } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// common component
import InputRow from '../../common/InputRow';
import CalculatedRow from '../../common/CalculatedRow';
import CustomInputAccessoryView from '../../../../../Accounts/AddEdit/CustomInput';

// constants
import { BLACK_INFO_ICON } from '../../../../../../constants/AssetSVGConstants';
import ROUTE_CONSTANTS from '../../../../../../constants/RouteConstants';

import {
  FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES,
  FORAGE_INVENTORIES_INPUT_MAX_LIMIT,
  FORAGE_INVENTORIES_INPUT_MIN_LIMIT,
  FORAGE_INVENTORIES_SILO_HEIGHT_MAX_LIMIT_METRIC,
  FORAGE_INVENTORIES_SILO_HEIGHT_MAX_LIMIT_IMPERIAL,
  FORAGE_INVENTORIES_DRY_MATTER_MAX_LIMIT,
  NEXT_FIELD_TEXT,
  UNIT_OF_MEASURE,
} from '../../../../../../constants/AppConstants';
import {
  CONTENT_TYPE,
  FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS,
} from '../../../../../../constants/FormConstants';
import { FORAGE_INVENTORIES_TYPES } from '../../../../../../constants/toolsConstants/ForageInventories';

import {
  getAltDistanceText,
  getDensityUnit,
  getDistanceUnit,
  getFormImageByLocale,
  getLabelWithUnit,
  getTopUnloadingSilageDMDensity,
  getTopUnloadingTonsAF,
  getTopUnloadingTonsDM,
  getUnitOfMeasure,
} from '../../../../../../helpers/forageInventoriesHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const TopUnloadingSiloCapacity = props => {
  const { screenDisabled, values, references, handleChange } = props;
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const navigation = useNavigation();
  const userData = useSelector(
    state => state?.userPreferences?.userPreferences,
  );

  const selectedUnit = getUnitOfMeasure(userData);
  const distanceUnit = getDistanceUnit(userData);
  const densityUnit = getDensityUnit(userData);

  const openDensityConverterScreen = () => {
    navigation.navigate(ROUTE_CONSTANTS.DENSITY_CONVERTER);
  };

  return (
    <View style={styles.container}>
      <CustomInputAccessoryView doneAction={action} type={type} />
      <View style={styles.imageContainer}>
        {/* <TOP_UNLOADING_ENGLISH_IMAGE {...styles.pileImage} /> */}
        {getFormImageByLocale(
          FORAGE_INVENTORIES_TYPES.TOP_UNLOADING_SILO,
          // styles.pileImage,
        )}
      </View>
      <View style={styles.pileDimensionRow}>
        <Text style={styles.pileDimensionText}>
          {i18n.t('topUnloadingSiloDimension')}
        </Text>
        <TouchableOpacity onPress={openDensityConverterScreen}>
          <BLACK_INFO_ICON {...styles.bigIcon} fill={styles.grey1} />
        </TouchableOpacity>
      </View>

      {/* Filled height */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('fillHeight'), distanceUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={
            selectedUnit === UNIT_OF_MEASURE.IMPERIAL
              ? FORAGE_INVENTORIES_SILO_HEIGHT_MAX_LIMIT_IMPERIAL
              : FORAGE_INVENTORIES_SILO_HEIGHT_MAX_LIMIT_METRIC
          }
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          value={
            values[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.FILLED_HEIGHT
            ]?.toString() || ''
          }
          onChange={handleChange(
            FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.FILLED_HEIGHT,
          )}
          reference={ref =>
            (references[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.FILLED_HEIGHT
            ] = ref)
          }
          onSubmitEditing={() => {
            references[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.HEIGHT_OF_SILAGE_LEFT
            ]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.FILLED_HEIGHT],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[
                  FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS
                    .HEIGHT_OF_SILAGE_LEFT
                ],
            });
          }}
        />
      </View>

      {/* Height of silage left in silo */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(
            i18n.t('heightOfSilageLeftInSilo'),
            distanceUnit,
          )}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={
            selectedUnit === UNIT_OF_MEASURE.IMPERIAL
              ? FORAGE_INVENTORIES_SILO_HEIGHT_MAX_LIMIT_IMPERIAL
              : FORAGE_INVENTORIES_SILO_HEIGHT_MAX_LIMIT_METRIC
          }
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          value={
            values[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.HEIGHT_OF_SILAGE_LEFT
            ]?.toString() || ''
          }
          onChange={handleChange(
            FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.HEIGHT_OF_SILAGE_LEFT,
          )}
          reference={ref =>
            (references[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.HEIGHT_OF_SILAGE_LEFT
            ] = ref)
          }
          onSubmitEditing={() => {
            references[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.DIAMETER
            ]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.HEIGHT_OF_SILAGE_LEFT
            ],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[
                  FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.DIAMETER
                ],
            });
          }}
        />
      </View>

      {/* Diameter % */}
      <View style={styles.formInputView}>
        <InputRow
          title={getLabelWithUnit(i18n.t('diameter'), distanceUnit)}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_INPUT_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          value={
            values[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.DIAMETER
            ]?.toString() || ''
          }
          onChange={handleChange(
            FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.DIAMETER,
          )}
          reference={ref =>
            (references[FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.DIAMETER] =
              ref)
          }
          onSubmitEditing={() => {
            references[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.DRY_MATTER
            ]?.focus();
          }}
          blurOnSubmit={false}
          altUnitText={getAltDistanceText(
            values[FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.DIAMETER],
            selectedUnit,
          )}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              currentRef:
                references[
                  FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.DRY_MATTER
                ],
            });
          }}
        />
      </View>

      <View style={styles.divider}></View>

      {/* Dry Matter % */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <InputRow
          title={i18n.t('dryMatter')}
          disabled={screenDisabled}
          placeholder={i18n.t('singleDecimalNumberPlaceholder')}
          minValue={FORAGE_INVENTORIES_INPUT_MIN_LIMIT}
          maxValue={FORAGE_INVENTORIES_DRY_MATTER_MAX_LIMIT}
          isInteger={false}
          decimalPoints={FORAGE_INVENTORIES_INPUT_DECIMAL_PLACES}
          value={
            values[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.DRY_MATTER
            ]?.toString() || ''
          }
          onChange={handleChange(
            FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.DRY_MATTER,
          )}
          reference={ref =>
            (references[
              FORAGE_INVENTORIES_TOP_UNLOADING_SILO_FIELDS.DRY_MATTER
            ] = ref)
          }
          onSubmitEditing={() => {
            Keyboard.dismiss();
          }}
          blurOnSubmit={false}
          hideAltText={true}
          inputAccessoryViewID="customInputAccessoryView"
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          onFocus={() => {
            setType(CONTENT_TYPE.NUMBER);
            setAction({
              dismiss: true,
            });
          }}
        />
      </View>

      {/* Silage DM Density */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <CalculatedRow
          title={`${getLabelWithUnit(i18n.t('silageDMDensity'), densityUnit)}`}
          value={convertInputNumbersToRegionalBasis(
            getTopUnloadingSilageDMDensity(values, selectedUnit, true)
              .toFixed(1)
              .toString(),
            1,
          )}
        />
      </View>
      <Text style={styles.pileDimensionText}>{i18n.t('capacity')}</Text>

      {/* Tons DM */}
      <View style={[styles.formInputView, styles.marginTop]}>
        <CalculatedRow
          title={`${getLabelWithUnit(i18n.t('tonsDM'))} ${i18n.t(
            'remainingInSilo',
          )}`}
          value={convertInputNumbersToRegionalBasis(
            getTopUnloadingTonsDM(values, selectedUnit, true)
              .toFixed(1)
              .toString(),
            1,
          )}
        />
      </View>

      {/* Tons AF */}
      <View style={styles.formInputView}>
        <CalculatedRow
          title={`${i18n.t('tonsAF')} ${i18n.t('remainingInSilo')}`}
          value={convertInputNumbersToRegionalBasis(
            getTopUnloadingTonsAF(values, selectedUnit, true)
              .toFixed(1)
              .toString(),
            1,
          )}
        />
      </View>
    </View>
  );
};

export default TopUnloadingSiloCapacity;
