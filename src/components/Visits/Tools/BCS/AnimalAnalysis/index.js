// modules
import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, FlatList, Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

// styles
import styles from './styles';

// localization
import i18n from '../../../../../localization/i18n';

// reusable components
import CustomBottomSheet from '../../../../common/CustomBottomSheet';
import AnimalCard from '../../../../Tools/AnimalCard';
import { showAlertMsg } from '../../../../common/Alerts';
import AnimalAnalysisResults from './AnimalAnalysisResults';
import { showToast as toastComponent } from '../../../../../components/common/CustomToast';

// constants
import ROUTE_CONSTANTS from '../../../../../constants/RouteConstants';
import {
  BCS_SCALE_ICON,
  CHEVRON_DOWN_BLUE_ICON,
  CLOSE_ICON,
  FILL_INFO_ICON,
  CHEVRON_DOWN_GRAY_ICON,
} from '../../../../../constants/AssetSVGConstants';
import {
  BOTTOM_SHEET_TYPE,
  TOAST_TYPE,
} from '../../../../../constants/FormConstants';
import { normalize } from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';
import {
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
  VISIT_TABLE_FIELDS,
} from '../../../../../constants/AppConstants';

// actions
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
  setSelectedPenRequest,
} from '../../../../../store/actions/tool';
import {
  getUnfilteredEarTagsRequest,
  resetGetEarTagsRequest,
  resetGetUnfilteredEarTagsRequest,
} from '../../../../../store/actions/earTag';
import { hideBCSToastRequest } from '../../../../../store/actions/userPreferences';
import { updateScaleInSite } from '../../../../../store/actions/site';

// helpers
import { stringIsEmpty } from '../../../../../helpers/alphaNumericHelper';
import {
  getEarTagById,
  getFormattedAnimalData,
} from '../../../../../helpers/toolHelper';
import { mapGraphDataForAnimalAnalysisExport } from '../../../../../helpers/locomotionHelper';
import {
  pickPenInReducerFromPensList,
  saveSelectedPenInReducer,
} from '../../../../../helpers/visitHelper';

// services
import { isOnline } from '../../../../../services/netInfoService';
import {
  getAnimalsRequest,
  deleteAnimalRequest,
  resetDeleteAnimalRequest,
  clearAnimalAnalysisRequest,
  resetClearAnimalAnalysisRequest,
} from '../../../../../store/actions/tools/animalAnalysis';

const AnimalAnalysis = props => {
  const {
    penList,
    scaleList,
    screenDisabled,
    isBCS,
    currentStep,
    totalSteps,
    selectedVisits,
    isLocomotion,
    healthCurrentActivePen,
    setEnableResults,
  } = props;
  const [selectedPen, setSelectedPen] = useState(null);
  const [selectedScale, setSelectedScale] = useState(null);
  const [showPenBottomSheet, setShowPenBottomSheet] = useState(false);
  const [showScaleBottomSheet, setShowScaleBottomSheet] = useState(false);
  const [animalList, setAnimalList] = useState([]);
  const [showToast, setShowToast] = useState(false);

  const navigation = useNavigation();
  const dispatch = useDispatch();

  const visitState = useSelector(state => state.visit);
  const animalAnalysisState = useSelector(state => state.animalAnalysis);
  const earTagState = useSelector(state => state.earTag);
  const userPreferencesState = useSelector(state => state.userPreferences);

  const { isEditable = false } = visitState?.visit || false;

  useEffect(() => {
    if (userPreferencesState?.userPreferences?.showBCSAnimalAnalysisToast) {
      if (userPreferencesState?.userPreferences?.showBCSAnimalAnalysisToast) {
        setShowToast(true);
      }
    }
    const { siteId, localSiteId, customerId, localCustomerId, id } =
      visitState.visit || {};
    dispatch(
      getUnfilteredEarTagsRequest({
        accountId: !stringIsEmpty(customerId) ? customerId : localCustomerId,
        siteId: !stringIsEmpty(siteId) ? siteId : localSiteId,
      }),
    );
    dispatch(getAnimalsRequest({ localId: id }));
    return () => {
      dispatch(resetGetEarTagsRequest());
      dispatch(resetGetUnfilteredEarTagsRequest());
    };
    // TODO: Show info toast message until user clears the message. Will follow the onboarding storage logic
  }, []);

  useEffect(() => {
    if (penList.length > 0) {
      // setSelectedPen(penList[0]);
      if (healthCurrentActivePen) {
        pickPenInReducerFromPensList(
          penList,
          healthCurrentActivePen,
          setSelectedPen,
        );
      } else {
        setSelectedPen(penList[0]);
      }
      if (animalAnalysisState.animalAnalysis?.animals) {
        const animals = animalAnalysisState.animalAnalysis?.animals;
        const filteredAnimals = getFilteredAnimals(animals, penList[0]);
        setAnimalList(filteredAnimals);
      }
    }
  }, [penList]);

  //saves selected pen in reducer on pen change
  useEffect(() => {
    dispatch(setSelectedPenRequest(selectedPen));
  }, [selectedPen?.id, selectedPen?.sv_id]);

  useEffect(() => {
    if (scaleList.length > 0) {
      const selectedKey = visitState.visit.selectedPointScale;
      let scaleSet = false;
      for (const scale of scaleList) {
        if (scale.key === selectedKey) {
          setSelectedScale(scale);
          scaleSet = true;
          break;
        }
      }
      if (!scaleSet) {
        setSelectedScale(scaleList[0]);
      }
    }
  }, [scaleList]);

  //for disabling results button if no animals added
  useEffect(() => {
    setEnableResults && setEnableResults(animalList?.length > 0);
  }, [animalList]);

  const getFilteredAnimals = (data = [], pen) => {
    let filteredArray = [];
    if (!stringIsEmpty(pen.sv_id)) {
      filteredArray = data?.filter(
        obj => obj.penId === pen.sv_id || obj.localPenId === pen.sv_id,
      );
    } else {
      filteredArray = data.filter(obj => obj.localPenId === pen.id);
    }
    if (filteredArray && filteredArray.length > 0) {
      const penObject = filteredArray[0];
      let animals = penObject?.animalDetails || [];
      if (isBCS) {
        animals = animals.filter(animal => {
          return !stringIsEmpty(animal.bcsCategory);
        });
      } else if (isLocomotion) {
        animals = animals.filter(animal => {
          return !stringIsEmpty(animal.locomotionScore);
        });
      }
      return animals;
    }
    return [];
  };

  useEffect(() => {
    if (animalAnalysisState.animalAnalysis?.animals) {
      const animals = animalAnalysisState.animalAnalysis?.animals;
      if (selectedPen) {
        const filteredAnimals = getFilteredAnimals(animals, selectedPen);
        setAnimalList(filteredAnimals);
      }
    } else {
      setAnimalList([]);
    }
  }, [animalAnalysisState.animalAnalysis]);

  const fetchAnimalsData = () => {
    const { id } = visitState.visit || {};
    dispatch(getAnimalsRequest({ localId: id }));
  };

  useEffect(() => {
    if (animalAnalysisState.deleteAnimalSuccess) {
      dispatch(resetDeleteAnimalRequest());
      fetchAnimalsData();
    }
  }, [animalAnalysisState.deleteAnimalSuccess]);

  useEffect(() => {
    if (animalAnalysisState.resetAnimalAnalysisSuccess) {
      dispatch(resetClearAnimalAnalysisRequest());
      fetchAnimalsData();
    }
  }, [animalAnalysisState.resetAnimalAnalysisSuccess]);

  const openPenBottomSheet = () => {
    setShowPenBottomSheet(true);
  };

  const closePenBottomSheet = () => {
    setShowPenBottomSheet(false);
  };

  const onPenChange = item => {
    saveSelectedPenInReducer(dispatch, item);
    setSelectedPen(item);
    closePenBottomSheet();
    if (animalAnalysisState?.animalAnalysis?.animals) {
      const animals = animalAnalysisState?.animalAnalysis?.animals;
      const filteredAnimals = getFilteredAnimals(animals, item);
      setAnimalList(filteredAnimals);
    }
  };

  const openScaleBottomSheet = () => {
    setShowScaleBottomSheet(true);
  };

  const closeScaleBottomSheet = () => {
    setShowScaleBottomSheet(false);
  };

  const onScaleChange = item => {
    if (item.key !== selectedScale?.key) {
      showAlertMsg('', i18n.t('scaleChangeMessage'), [
        {
          text: i18n.t('no'),
          onPress: () => {},
        },
        {
          text: i18n.t('yes'),
          onPress: () => {
            const { id } = visitState.visit;
            setSelectedScale(item);
            dispatch(
              clearAnimalAnalysisRequest({
                localId: id,
                selectedPointScale: item.key,
              }),
            );
            // Update scale in site
            dispatch(
              updateScaleInSite({
                localSiteId: visitState.visit.localSiteId,
                siteId: visitState.visit.siteId,
                scale: item.key,
              }),
            );
          },
        },
      ]);
    }
    closeScaleBottomSheet();
  };

  const getAnimalScreenTitle = () => {
    return `${i18n.t('animal')} ${animalList?.length + 1}`;
  };

  const moveToAddAnimalScreen = (animal, animalTitle) => {
    const navObj = {
      bcsScale: selectedScale,
      pen: selectedPen,
      screenTitle: getAnimalScreenTitle(),
      toolType: isBCS
        ? VISIT_TABLE_FIELDS.BODY_CONDITION
        : VISIT_TABLE_FIELDS.LOCOMOTION_SCORE,
    };
    if (animal) {
      navObj.editMode = true;
      navObj.screenTitle = animalTitle;
      navObj.data = animal;
    }
    navigation.navigate(ROUTE_CONSTANTS.ADD_ANIMAL, navObj);
  };

  const deleteAnimalHandler = animalData => {
    const { id } = visitState.visit || {};
    dispatch(deleteAnimalRequest({ animal: animalData, localVisitId: id }));
  };

  const renderItem = ({ item, index }) => {
    return (
      <AnimalCard
        title={`${i18n.t('animal')} ${index + 1}`}
        onCardPress={() => {
          moveToAddAnimalScreen(item, `${i18n.t('animal')} ${index + 1}`);
        }}
        onDeletePress={() => {
          showAlertMsg('', i18n.t('deleteAnimalMessage'), [
            {
              text: i18n.t('no'),
              onPress: () => {},
            },
            {
              text: i18n.t('yes'),
              onPress: () => deleteAnimalHandler(item),
            },
          ]);
        }}
        earTag={getEarTagById(item, earTagState?.allEarTagList)}
        daysInMilk={item.daysInMilk}
        bcs={item.bcsCategory}
        locomotion={item.locomotionScore}
        customContainerStyle={styles.animalCardContainer}
        screenDisabled={screenDisabled}
      />
    );
  };

  const downloadAnimalAnalysisData = async (
    graphData,
    type,
    selectedAnimal,
    selectedPen,
    bcsSelected,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForAnimalAnalysisExport(
        visitState?.visit,
        graphData,
        selectedAnimal,
        selectedPen,
        bcsSelected,
      );

      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType:
              EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_ANIMAL_ANALYSIS_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType:
              EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_ANIMAL_ANALYSIS_REPORT,
            model,
          }),
        );
      }
    } else {
      toastComponent(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onShareAnimalAnalysisData = async (
    graphData,
    type,
    selectedAnimal,
    selectedPen,
    bcsSelected,
    currentVisitDIM,
    exportMethod,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForAnimalAnalysisExport(
        visitState.visit,
        graphData,
        selectedAnimal,
        selectedPen,
        bcsSelected,
      );

      //share excel
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod == GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType:
              EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_ANIMAL_ANALYSIS_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType:
            EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_ANIMAL_ANALYSIS_REPORT,
          model,
        }),
      );
    } else {
      toastComponent(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const renderAnimalAnalysisResults = () => {
    const { id, visitDate } = visitState.visit || {};

    return (
      <AnimalAnalysisResults
        selectedPen={selectedPen}
        animalData={{
          animalDetails: getFormattedAnimalData(
            animalList,
            earTagState.allEarTagList,
          ),
          visitId: id,
          date: visitDate,
        }}
        selectedVisits={selectedVisits}
        isLocomotion={isLocomotion}
        isBCS={isBCS}
        onDownloadPress={downloadAnimalAnalysisData}
        onShareAnimalAnalysisData={onShareAnimalAnalysisData}
      />
    );
  };

  const onCloseToast = () => {
    setShowToast(false);
    dispatch(hideBCSToastRequest());
  };

  return (
    <>
      {currentStep === totalSteps ? (
        renderAnimalAnalysisResults()
      ) : (
        <View style={styles.container}>
          <View style={styles.headerContainer}>
            <View>
              <TouchableOpacity
                style={styles.dropdownTextContainer}
                onPress={openPenBottomSheet}>
                <Text
                  style={[styles.dropdownText, styles.penNameLimit]}
                  noOfLines={1}>
                  {selectedPen?.name || ''}
                </Text>
                <CHEVRON_DOWN_BLUE_ICON
                  width={normalize(12)}
                  height={normalize(8)}
                />
              </TouchableOpacity>
            </View>
            {isBCS && (
              <View>
                <TouchableOpacity
                  style={styles.dropdownTextContainer}
                  onPress={openScaleBottomSheet}
                  disabled={screenDisabled}>
                  <View style={styles.scaleIconContainer}>
                    <BCS_SCALE_ICON
                      width={normalize(13)}
                      height={normalize(13)}
                    />
                  </View>
                  <Text
                    style={[
                      styles.dropdownText,
                      !isEditable && styles.disableColor,
                    ]}>
                    {selectedScale?.name || ''}
                  </Text>
                  {isEditable ? (
                    <CHEVRON_DOWN_BLUE_ICON
                      width={normalize(12)}
                      height={normalize(8)}
                    />
                  ) : (
                    <CHEVRON_DOWN_GRAY_ICON
                      width={normalize(12)}
                      height={normalize(8)}
                    />
                  )}
                </TouchableOpacity>
              </View>
            )}
          </View>
          <FlatList
            style={styles.flatlist}
            data={animalList}
            showsVerticalScrollIndicator={false}
            renderItem={renderItem}
            ListFooterComponent={
              screenDisabled ? null : (
                <TouchableOpacity
                  style={styles.buttonContainer}
                  onPress={() => moveToAddAnimalScreen()}>
                  <Text style={styles.plusIcon}>{i18n.t('plusSign')}</Text>
                  <Text style={styles.addAnimalText}>
                    {i18n.t('addAnimal')}
                  </Text>
                </TouchableOpacity>
              )
            }
          />
          {showToast && (
            <View style={styles.toastContainer}>
              <View style={styles.infoIconContainer}>
                <FILL_INFO_ICON
                  width={normalize(20)}
                  height={normalize(20)}
                  fill={colors.white}
                />
              </View>
              <Text style={styles.toastMessageText}>
                {i18n.t('bcsDataTransferMessage')}
              </Text>
              <TouchableOpacity
                style={styles.closeIconStyle}
                onPress={onCloseToast}>
                <CLOSE_ICON
                  width={normalize(9)}
                  height={normalize(14)}
                  fill={colors.white}
                />
              </TouchableOpacity>
            </View>
          )}

          {showPenBottomSheet && (
            <CustomBottomSheet
              type={BOTTOM_SHEET_TYPE.SIMPLE}
              selectLabel={i18n.t('selectPen')}
              searchPlaceHolder={i18n.t('searchPen')}
              data={penList}
              onChange={onPenChange}
              onClose={closePenBottomSheet}
            />
          )}
          {showScaleBottomSheet && (
            <CustomBottomSheet
              type={BOTTOM_SHEET_TYPE.SIMPLE}
              selectLabel={i18n.t('selectScale')}
              data={scaleList}
              disableSearch
              onChange={onScaleChange}
              onClose={closeScaleBottomSheet}
              customContainerStyle={styles.scaleBottomSheetContainer}
            />
          )}
        </View>
      )}
    </>
  );
};

export default AnimalAnalysis;
