// modules
import React from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

//components
import ROFSummaryAccordionContent from './ROFSummaryAccordionContent';

// styles
import styles from './styles';

const ROFSummaryAccordionContainer = props => {
  const rofToolData = useSelector(state => state.rof?.rofToolData);
  const previousROFVisitData = useSelector(state => state.rof?.previousROFVisitData?.returnOverFeed);
  const selectedCategoryTool = useSelector(
    state => state.tool.selectedCategoryTool,
  );
  let summaryData = rofToolData?.[selectedCategoryTool?.toolType]?.summary;
  let prevSummaryData = JSON.parse(previousROFVisitData)?.[selectedCategoryTool?.toolType]?.summary;
  // console.log('summaryData', rofToolData);
  console.log('previousROFVisitData', prevSummaryData);

  return (
    <View>
      <View style={styles.formContainer}>
        {summaryData &&
          Object.entries(summaryData)?.map(([sectionKey, fields]) => {
            return (
              <ROFSummaryAccordionContent
                key={'_ROFSummaryAccordion-' + sectionKey}
                sectionKey={sectionKey}
                data={fields}
                summaryData={summaryData}
              />
            );
          })}
      </View>
    </View>
  );
};

export default ROFSummaryAccordionContainer;
