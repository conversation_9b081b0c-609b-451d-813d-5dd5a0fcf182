import { StyleSheet } from 'react-native';
import fonts, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../constants/theme/variables/customColor';

export default StyleSheet.create({
  container: {
    marginHorizontal: normalize(16.5),
    marginTop: normalize(8),
    marginBottom: normalize(8),
  },
  headerRow: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderBottomColor: colors.accordionBorder,
    paddingVertical: normalize(8),
    paddingHorizontal: normalize(8),
  },
  dataRow: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderBottomColor: colors.accordionBorder,
    paddingVertical: normalize(12),
  },
  labelColumn: {
    flex: 2,
    justifyContent: 'center',
    paddingRight: normalize(8),
  },
  valueColumn: {
    flex: 1,
    alignItems: 'center',
    width: '100%',
  },
  headerText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(12),
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    color: colors.primaryMain,
    textAlign: 'center',
  },
  labelText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    color: colors.grey1,
  },
  valueBoxCurrent: {
    backgroundColor: '#DDF0FD',
    width: normalize(74),
    height: normalize(32),
    justifyContent: 'center',
    alignItems: 'center',
  },
  valueBoxPrev: {
    backgroundColor: '#F0F9FE',
    width: normalize(74),
    height: normalize(32),
    justifyContent: 'center',
    alignItems: 'center',
  },
  valueText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    color: colors.black,
    textAlign: 'center',
  },
});
