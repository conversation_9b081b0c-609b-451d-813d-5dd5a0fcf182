// modules
import React, { useState } from 'react';
import { View, Text, Keyboard } from 'react-native';
import { useSelector } from 'react-redux';
import { useFormikContext } from 'formik';

import NumberFormInput from '../../../../../../common/NumberFormInput';
import CustomBottomSheet from '../../../../../../common/CustomBottomSheet';
import InputRow from '../../../../common/InputRow';
import AddNewIngredientButton from './addNewIngredientButton';
import DeleteItemAlert from '../../../../../../common/DeleteItemAlert';
import FormButton from '../../../../../../common/FormButton';

import styles from './styles';

import {
  BOTTOM_SHEET_TYPE,
  BUTTON_TYPE,
  ROF_FIELDS,
} from '../../../../../../../constants/FormConstants';
import {
  ENUM_CONSTANTS,
  NEXT_FIELD_TEXT,
} from '../../../../../../../constants/AppConstants';
import {
  ROF_DECIMAL_PLACES,
  ROF_COMMA_SEPARATED_MAX_VALUE,
  ROF_FEEDING_INGREDIENTS_TYPES,
  ROF_INTEGER_MIN_VALUE,
  ROF_DRY_MATTER_MAX_VALUE,
  ROF_PRICE_LIST_TYPES,
  ROF_FORM_TYPES,
} from '../../../../../../../constants/toolsConstants/ROFConstants';

import i18n from '../../../../../../../localization/i18n';

import {
  getSubLabelForPricePerTon,
  setIngredientFieldsByTypeInArray,
} from '../../../../../../../helpers/rofHelper';
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../../helpers/appSettingsHelper';
import { logEvent } from '../../../../../../../helpers/logHelper';

const FeedingIngredientsForm = props => {
  let {
    isEditable,
    title,
    buttonText,
    type,
    ingredientNameTitle,
    formType,
    setInputRef,
    focusNextField,
    parentIndex,
  } = props;
  let { setFieldValue, values } = useFormikContext();

  const enumState = useSelector(state => state.enums.enum);
  const rofPriceList = useSelector(state => state.rof?.rofPriceList);
  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  const [deletableItemIndex, setDeletableItemIndex] = useState(null);

  let priceList =
    rofPriceList[
      type == ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_FORAGES
        ? ROF_PRICE_LIST_TYPES.HOME_GROWN_FORAGES
        : ROF_PRICE_LIST_TYPES.HOME_GROWN_GRAINS
    ];

  const onDeletePress = index => {
    try {
      setIngredientFieldsByTypeInArray(
        formType,
        setFieldValue,
        values,
        type,
        null,
        null,
        index,
        priceList,
        true,
        enumState,
      );
      setDeletableItemIndex(null);
    } catch (error) {
      logEvent('onDeletePress > ROF Feeding', error);
    }
  };

  let renderIngredientsForm = (item, index) => {
    return (
      <View style={styles.ingredientContainer} key={index}>
        {(type == ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_FORAGES ||
          type == ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_GRAINS) && (
          <View style={styles.dropdownWithDeleteContainer}>
            <View style={styles.dropdownContainer}>
              <CustomBottomSheet
                type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
                placeholder={i18n.t('select')}
                disabled={!isEditable}
                value={
                  values[ROF_FIELDS.FEEDING][type][index][
                    type == ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_FORAGES
                      ? ROF_FIELDS.HOME_GROWN_FORAGE_TYPE
                      : ROF_FIELDS.HOME_GROWN_GRAINS_TYPE
                  ]
                }
                infoText={i18n.t('selectOne')}
                selectLabel={i18n.t('select')}
                data={
                  enumState?.[
                    type == ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_FORAGES
                      ? ENUM_CONSTANTS.HOME_GROWN_FORAGE_TYPES
                      : ENUM_CONSTANTS.HOME_GROWN_GRAIN_TYPES
                  ]
                }
                onChange={v =>
                  setIngredientFieldsByTypeInArray(
                    formType,
                    setFieldValue,
                    values,
                    type,
                    type == ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_FORAGES
                      ? ROF_FIELDS.HOME_GROWN_FORAGE_TYPE
                      : ROF_FIELDS.HOME_GROWN_GRAINS_TYPE,
                    v.key,
                    index,
                    priceList,
                    true,
                  )
                }
                customInputStyle={{ marginBottom: 0 }}
              />
            </View>
          </View>
        )}
        <InputRow
          title={ingredientNameTitle}
          disabled={!isEditable}
          isRequired
          placeholder={i18n.t('enterValue')}
          value={
            values[ROF_FIELDS.FEEDING][type][index][
              type == ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_FORAGES
                ? ROF_FIELDS.FORAGE_NAME
                : type == ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_GRAINS
                ? ROF_FIELDS.GRAINS_NAME
                : ROF_FIELDS.FEED_NAME
            ]
          }
          onChange={v =>
            setIngredientFieldsByTypeInArray(
              formType,
              setFieldValue,
              values,
              type,
              type == ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_FORAGES
                ? ROF_FIELDS.FORAGE_NAME
                : type == ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_GRAINS
                ? ROF_FIELDS.GRAINS_NAME
                : ROF_FIELDS.FEED_NAME,
              v,
              index,
              priceList,
              true,
            )
          }
          blurOnSubmit={false}
          selectTextOnFocus={false}
          customInputContainerStyle={styles.formInputStyle}
          textAlign={'flex-start'}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          reference={ref => setInputRef(parentIndex, index, 0, ref)}
          onSubmitEditing={() => {
            setTimeout(() => focusNextField(parentIndex, index, 1), 100);
          }}
        />

        {/* number TODO: handle minValue = 1 */}
        <NumberFormInput
          label={`${i18n
            .t(
              formType === ROF_FORM_TYPES.TMR
                ? 'totalHerdPerDay'
                : 'totalCowPerDay',
            )
            .replace('kg', weightUnit)}`}
          required
          disabled={!isEditable}
          placeholder={i18n.t('numberPlaceholder')}
          minValue={ROF_INTEGER_MIN_VALUE}
          maxValue={ROF_COMMA_SEPARATED_MAX_VALUE}
          decimalPoints={ROF_DECIMAL_PLACES}
          hasCommas
          value={
            values[ROF_FIELDS.FEEDING][type][index][
              ROF_FIELDS.TOTAL_HERD_PER_DAY
            ]
          }
          onChange={v =>
            setIngredientFieldsByTypeInArray(
              formType,
              setFieldValue,
              values,
              type,
              ROF_FIELDS.TOTAL_HERD_PER_DAY,
              v,
              index,
              priceList,
              true,
            )
          }
          blurOnSubmit={false}
          selectTextOnFocus={false}
          customLabelStyle={styles.numberInputFieldLabel}
          customInputContainerStyle={styles.numberInputStyle}
          customContainerStyle={styles.numberInputContainerStyles}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          reference={ref => setInputRef(parentIndex, index, 1, ref)}
          onSubmitEditing={() => {
            setTimeout(() => focusNextField(parentIndex, index, 2), 100);
          }}
        />
        {/* number */}
        <NumberFormInput
          label={i18n.t('dryMatter')}
          required
          disabled={!isEditable}
          placeholder={i18n.t('numberPlaceholder')}
          minValue={ROF_INTEGER_MIN_VALUE}
          maxValue={ROF_DRY_MATTER_MAX_VALUE}
          decimalPoints={ROF_DECIMAL_PLACES}
          value={values[ROF_FIELDS.FEEDING][type][index][ROF_FIELDS.DRY_MATTER]}
          onChange={v =>
            setIngredientFieldsByTypeInArray(
              formType,
              setFieldValue,
              values,
              type,
              ROF_FIELDS.DRY_MATTER,
              v,
              index,
              priceList,
              true,
            )
          }
          blurOnSubmit={false}
          selectTextOnFocus={false}
          customLabelStyle={styles.numberInputFieldLabel}
          customInputContainerStyle={styles.numberInputStyle}
          customContainerStyle={styles.numberInputContainerStyles}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          reference={ref => setInputRef(parentIndex, index, 2, ref)}
          onSubmitEditing={() => {
            setTimeout(() => focusNextField(parentIndex, index, 3), 100);
          }}
        />
        {/* formula */}
        <NumberFormInput
          label={i18n.t('totalDryMatter').replace('kg', weightUnit)}
          disabled={true}
          placeholder={i18n.t('numberPlaceholder')}
          decimalPoints={ROF_DECIMAL_PLACES}
          value={
            values[ROF_FIELDS.FEEDING][type][index][ROF_FIELDS.TOTAL_DRY_MATTER]
          }
          blurOnSubmit={false}
          selectTextOnFocus={false}
          customLabelStyle={styles.numberInputFieldLabel}
          customInputContainerStyle={styles.numberInputStyle}
          customContainerStyle={styles.numberInputContainerStyles}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
        />
        {/* number */}
        <NumberFormInput
          label={`${i18n
            .t('pricePerTon')
            .replace('$', currencySymbol)
            .replaceAll('ppt', getSubLabelForPricePerTon(type))}`}
          disabled={!isEditable}
          placeholder={i18n.t('numberPlaceholder')}
          minValue={ROF_INTEGER_MIN_VALUE}
          maxValue={ROF_COMMA_SEPARATED_MAX_VALUE}
          decimalPoints={ROF_DECIMAL_PLACES}
          hasCommas
          value={
            values[ROF_FIELDS.FEEDING][type][index][ROF_FIELDS.PRICE_PER_TON]
          }
          onChange={v =>
            setIngredientFieldsByTypeInArray(
              formType,
              setFieldValue,
              values,
              type,
              ROF_FIELDS.PRICE_PER_TON,
              v,
              index,
              priceList,
              true,
            )
          }
          blurOnSubmit={false}
          selectTextOnFocus={false}
          customLabelStyle={styles.numberInputFieldLabel}
          customInputContainerStyle={styles.numberInputStyle}
          customContainerStyle={styles.numberInputContainerStyles}
          returnKeyType={NEXT_FIELD_TEXT.NEXT}
          reference={ref => setInputRef(parentIndex, index, 3, ref)}
          onSubmitEditing={() => {
            setTimeout(() => focusNextField(parentIndex, index, 4), 100);
          }}
        />

        <FormButton
          type={BUTTON_TYPE.PRIMARY}
          disabled={
            !isEditable ||
            ((type == ROF_FIELDS.HOME_GROWN_FORAGES ||
              type == ROF_FIELDS.HOME_GROWN_GRAINS) &&
              values[ROF_FIELDS.FEEDING][type].length == 1)
          }
          customButtonStyle={styles.deleteButton}
          onPress={() => {
            Keyboard.dismiss();
            setDeletableItemIndex(index);
          }}
          label={
            <Text
              style={[
                styles.deleteButtonText,
                (!isEditable ||
                  ((type == ROF_FIELDS.HOME_GROWN_FORAGES ||
                    type == ROF_FIELDS.HOME_GROWN_GRAINS) &&
                    values[ROF_FIELDS.FEEDING][type].length == 1)) &&
                  styles.disabledDeleteButtonText,
              ]}>
              {i18n.t('delete')}
            </Text>
          }
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.categoryHeader}>
        <Text style={styles.headerText}>{title}</Text>
      </View>
      {values[ROF_FIELDS.FEEDING][type]?.length > 0 &&
        values[ROF_FIELDS.FEEDING][type]?.map((item, index) => {
          return renderIngredientsForm(item, index);
        })}
      {values[ROF_FIELDS.FEEDING][type]?.length < 5 && (
        <AddNewIngredientButton
          isEditable={isEditable}
          buttonText={buttonText}
          addNewAccordionPress={() => {
            setIngredientFieldsByTypeInArray(
              formType,
              setFieldValue,
              values,
              type,
              null,
              null,
              null,
              priceList,
              true,
              enumState,
            );
          }}
        />
      )}

      <DeleteItemAlert
        isOpen={deletableItemIndex != null}
        onClose={() => setDeletableItemIndex(null)}
        onClick={() => onDeletePress(deletableItemIndex)}
      />
    </View>
  );
};

export default FeedingIngredientsForm;
