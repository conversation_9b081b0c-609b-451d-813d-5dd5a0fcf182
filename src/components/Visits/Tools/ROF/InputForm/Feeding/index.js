// modules
import React, { useEffect, useRef } from 'react';
import { Keyboard, Platform, View } from 'react-native';
import { useFormikContext } from 'formik';
import { useDispatch } from 'react-redux';

import NumberFormInput from '../../../../../common/NumberFormInput';
import FeedingIngredientsForm from './FeedingIngredientsForm';

import styles from './styles';

import i18n from '../../../../../../localization/i18n';

import {
  DAYS_IN_MILK_MAX_VALUE,
  DAYS_IN_MILK_MIN_VALUE,
  LACTATING_ANIMALS_MAX_VALUE,
  LACTATING_ANIMALS_MIN_VALUE,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';
import {
  ROF_FEEDING_INGREDIENTS_TYPES,
  ROF_FORM_TYPES,
} from '../../../../../../constants/toolsConstants/ROFConstants';
import {
  KEYBOARD_TYPE,
  ROF_FIELDS,
} from '../../../../../../constants/FormConstants';

import { logEvent } from '../../../../../../helpers/logHelper';
import {
  calculateAverageMilkProductionPerCowPerDay,
  handleKgPerCowInAllMilkingIngredients,
  recalculateTotalDryMatterInFeeding,
} from '../../../../../../helpers/rofHelper';

import { setIsSiteDataUpdated } from '../../../../../../store/actions/tools/rof';

const Feeding = props => {
  const inputRefs = useRef([]);

  let { isEditable, formType, openMilkProductionAccordion } = props;
  let { setFieldValue, values } = useFormikContext();
  const dispatch = useDispatch();

  useEffect(() => {
    inputRefs.current[0].focus();
  }, []);

  const setInputRef = (parentIndex, itemIndex, fieldIndex, node) => {
    try {
      if (!inputRefs.current[parentIndex]) {
        inputRefs.current[parentIndex] = [];
      }
      if (!inputRefs.current[parentIndex][itemIndex]) {
        inputRefs.current[parentIndex][itemIndex] = [];
      }

      inputRefs.current[parentIndex][itemIndex][fieldIndex] = node;
    } catch (e) {
      logEvent('ROF>feeding>setInputRef', {
        parentIndex,
        itemIndex,
        fieldIndex,
        node,
        e,
      });
    }
  };

  const focusNextField = (parentIndex, itemIndex, fieldIndex) => {
    try {
      inputRefs.current[parentIndex]?.[itemIndex]?.[fieldIndex]
        ? inputRefs.current[parentIndex]?.[itemIndex]?.[fieldIndex]?.focus()
        : inputRefs.current[parentIndex]?.[itemIndex + 1]?.[0]
        ? inputRefs.current[parentIndex]?.[itemIndex + 1]?.[0]?.focus()
        : inputRefs.current[parentIndex + 1]?.[0]?.[0]
        ? inputRefs.current[parentIndex + 1]?.[0]?.[0]?.focus()
        : openMilkProductionAccordion(true);
    } catch (e) {
      logEvent('ROF>feeding>focusNextField', {
        parentIndex,
        itemIndex,
        fieldIndex,
        e,
      });
    }
  };

  const handleLactatingCowsChange = v => {
    try {
      //set set lactating cows
      setFieldValue(`${ROF_FIELDS.FEEDING}.${ROF_FIELDS.LACTATING_COWS}`, v);
      //set avg milk production cow/day
      let milkProduction = calculateAverageMilkProductionPerCowPerDay(
        values[ROF_FIELDS.MILK_PRODUCTION][
          ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG
        ],
        v,
        true,
      );
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.MILK_PRODUCTION_KG}`,
        milkProduction,
      );
      //set price per kg cow in all milking ingredients
      handleKgPerCowInAllMilkingIngredients(
        setFieldValue,
        milkProduction,
        values[ROF_FIELDS.MILK_PRODUCTION],
      );
      if (formType === ROF_FORM_TYPES.INDIVIDUAL_COWS) {
        //recalculate all the total dry matter in feedings.
        recalculateTotalDryMatterInFeeding(values, v, setFieldValue);
      }
      dispatch(setIsSiteDataUpdated(true));
    } catch (e) {
      logEvent('handleLactatingCowsChange fail', e);
    }
  };

  const handleDaysInMilkChange = v => {
    try {
      //set set days in milk
      setFieldValue(`${ROF_FIELDS.FEEDING}.${ROF_FIELDS.DAYS_IN_MILK}`, v);

      dispatch(setIsSiteDataUpdated(true));
    } catch (e) {
      logEvent('handleDaysInMilkChange fail', e);
    }
  };

  return (
    <View style={styles.container}>
      {/* from site setup */}
      <NumberFormInput
        label={i18n.t('lactatingCows')}
        disabled={!isEditable}
        placeholder={i18n.t('numberPlaceholder')}
        isInteger
        hasCommas
        required
        minValue={LACTATING_ANIMALS_MIN_VALUE}
        maxValue={LACTATING_ANIMALS_MAX_VALUE}
        value={values[ROF_FIELDS.FEEDING][ROF_FIELDS.LACTATING_COWS]}
        onChange={handleLactatingCowsChange}
        selectTextOnFocus={false}
        blurOnSubmit={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        reference={reference => (inputRefs.current[0] = reference)}
        onSubmitEditing={() => {
          setTimeout(() => inputRefs.current[1].focus(), 100);
        }}
      />
      {/* from site setup */}
      <NumberFormInput
        label={i18n.t('daysInMilk')}
        disabled={!isEditable}
        placeholder={i18n.t('numberPlaceholder')}
        selectTextOnFocus={false}
        minValue={DAYS_IN_MILK_MIN_VALUE}
        maxValue={DAYS_IN_MILK_MAX_VALUE}
        isNegative={true}
        value={values[ROF_FIELDS.FEEDING][ROF_FIELDS.DAYS_IN_MILK]}
        onChange={handleDaysInMilkChange}
        keyboardType={
          Platform.OS === 'ios'
            ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
            : KEYBOARD_TYPE.NUMBER_PAD
        }
        blurOnSubmit={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        reference={reference => (inputRefs.current[1] = reference)}
        onSubmitEditing={() => {
          setTimeout(() => inputRefs.current[0][0][0].focus(), 100);
        }}
      />
      <FeedingIngredientsForm
        {...props}
        title={i18n.t('homeGrownForages')}
        buttonText={i18n.t('addHomeGrownForages')}
        type={ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_FORAGES}
        atleastOneRequired
        ingredientNameTitle={i18n.t('forageName')}
        formType={formType}
        setInputRef={setInputRef}
        focusNextField={focusNextField}
        parentIndex={0}
      />
      <FeedingIngredientsForm
        {...props}
        title={i18n.t('homeGrownGrains')}
        buttonText={i18n.t('addHomeGrownGrains')}
        type={ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_GRAINS}
        atleastOneRequired
        ingredientNameTitle={i18n.t('grainName')}
        formType={formType}
        setInputRef={setInputRef}
        focusNextField={focusNextField}
        parentIndex={1}
      />
      <FeedingIngredientsForm
        {...props}
        title={i18n.t('purchaseBulkFeed')}
        buttonText={i18n.t('addPurchaseBulkFeed')}
        type={ROF_FEEDING_INGREDIENTS_TYPES.PURCHASE_BULK_FEED}
        ingredientNameTitle={i18n.t('feedName')}
        formType={formType}
        setInputRef={setInputRef}
        focusNextField={focusNextField}
        parentIndex={2}
      />

      <FeedingIngredientsForm
        {...props}
        title={i18n.t('purchaseBagsFeed')}
        buttonText={i18n.t('addPurchaseBagsFeed')}
        type={ROF_FEEDING_INGREDIENTS_TYPES.PURCHASE_BAG_FEED}
        ingredientNameTitle={i18n.t('feedName')}
        formType={formType}
        setInputRef={setInputRef}
        focusNextField={focusNextField}
        parentIndex={3}
      />
    </View>
  );
};

export default Feeding;
