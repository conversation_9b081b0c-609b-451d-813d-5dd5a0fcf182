// modules
import React from 'react';
import { View } from 'react-native';
import { useFormikContext } from 'formik';
import { useSelector } from 'react-redux';

// styles
import styles from './styles';

// components
import NumberFormInput from '../../../../../common/NumberFormInput';

// localization
import i18n from '../../../../../../localization/i18n';

import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../helpers/appSettingsHelper';

// constants
import { ROF_FIELDS } from '../../../../../../constants/FormConstants';
import { NEXT_FIELD_TEXT } from '../../../../../../constants/AppConstants';

const MilkProductionOutputs = props => {

  let { values } = useFormikContext();
  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  const milkProductionOutputsKeys = Object.keys(
    values[ROF_FIELDS.MILK_PRODUCTION_OUTPUTS],
  );

  return (
    <View style={styles.container}>
      {milkProductionOutputsKeys?.map(outputKey => {
        return (
          <NumberFormInput
            label={i18n
              .t(outputKey)
              .replaceAll('$', currencySymbol)
              .replaceAll('kg', weightUnit)}
            disabled={true}
            hasCommas
            value={values[ROF_FIELDS.MILK_PRODUCTION_OUTPUTS][outputKey]}
            blurOnSubmit={true}
            selectTextOnFocus={false}
            customLabelStyle={styles.numberInputFieldLabel}
            customInputContainerStyle={styles.numberInputStyle}
            customContainerStyle={styles.numberInputContainerStyles}
            returnKeyType={NEXT_FIELD_TEXT.DONE}
          />
        );
      })}
    </View>
  );
};

export default MilkProductionOutputs;
