// modules
import React, { useRef, useEffect } from 'react';
import { View } from 'react-native';
import { useFormikContext } from 'formik';
import { useSelector } from 'react-redux';

import NumberFormInput from '../../../../../common/NumberFormInput';
import MilkingIngredients from './MilkingIngredients';

import styles from './styles';

import { ROF_FIELDS } from '../../../../../../constants/FormConstants';
import {
  ANIMALS_IN_TANK_MAX_VALUE,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';
import {
  ROF_DECIMAL_PLACES,
  ROF_INTEGER_MAX_VALUE,
  ROF_INTEGER_MIN_VALUE,
} from '../../../../../../constants/toolsConstants/ROFConstants';
import { ROF_MILKING_INGREDIENTS_TYPES } from '../../../../../../constants/toolsConstants/ROFConstants';

import i18n from '../../../../../../localization/i18n';

import { getWeightUnitByMeasure } from '../../../../../../helpers/appSettingsHelper';
import { logEvent } from '../../../../../../helpers/logHelper';
import {
  calculateAverageMilkProductionPerCowPerDay,
  calculateCurrentQuotaUtilizationKgPerDay,
  calculateTotalQuotaKgPerDay,
  handleKgPerCowInAllMilkingIngredients,
} from '../../../../../../helpers/rofHelper';

const MilkProduction = props => {
  const inputRefs = useRef([]);

  let { isEditable, openMilkProductionOutputsAccordion } = props;
  let { values, setFieldValue } = useFormikContext();
  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  useEffect(() => {
    inputRefs.current[0].focus();
  }, []);

  const handleAverageMilkProductionAnimalsInTankChange = v => {
    try {
      //set avg milk production per kg
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG}`,
        v,
      );

      //set currentQuotaUtilizationKgPerDay
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.CURRENT_QUOTA_UTILIZATION_KG_PER_DAY}`,
        calculateCurrentQuotaUtilizationKgPerDay(
          v,
          values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.BUTTERFAT][
            ROF_FIELDS.PERCENTAGE_PER_HL
          ],
          true,
        ),
      );

      let milkProductionLitersPerCowPerDay =
        calculateAverageMilkProductionPerCowPerDay(
          v,
          values[ROF_FIELDS.FEEDING][ROF_FIELDS.LACTATING_COWS],
          true,
        );
      //set avg milk production cow/day
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.MILK_PRODUCTION_KG}`,
        milkProductionLitersPerCowPerDay,
      );

      //set price per kg cow in all milking ingredients
      handleKgPerCowInAllMilkingIngredients(
        setFieldValue,
        milkProductionLitersPerCowPerDay,
        values[ROF_FIELDS.MILK_PRODUCTION],
      );
    } catch (e) {
      console.log('handleAverageMilkProductionAnimalsInTankChange fail', e);
      logEvent('handleAverageMilkProductionAnimalsInTankChange fail', e);
    }
  };

  const handleTotalQuotaKgPerDayCalculation = (
    kgOfQuotaPerDay = 0,
    incentiveDaysKgPerDay = 0,
  ) => {
    try {
      //calculate total quota kg per day
      let totalQuotaKgPerDay = calculateTotalQuotaKgPerDay(
        kgOfQuotaPerDay,
        incentiveDaysKgPerDay,
        true,
      );

      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY}`,
        totalQuotaKgPerDay,
      );
    } catch (e) {
      console.log('handleTotalQuotaKgPerDayCalculation fail', e);
      logEvent('handleTotalQuotaKgPerDayCalculation fail', e);
    }
  };

  const setInputRef = (parentIndex, fieldIndex, node) => {
    try {
      if (!inputRefs.current[parentIndex]) {
        inputRefs.current[parentIndex] = [];
      }

      inputRefs.current[parentIndex][fieldIndex] = node;
    } catch (e) {
      logEvent('ROF>milk production>setInputRef', {
        parentIndex,
        fieldIndex,
        node,
        e,
      });
    }
  };

  const focusNextField = (parentIndex, fieldIndex) => {
    try {
      inputRefs.current[parentIndex]?.[fieldIndex]
        ? inputRefs.current[parentIndex]?.[fieldIndex]?.focus()
        : openMilkProductionOutputsAccordion(true);
    } catch (e) {
      logEvent('ROF>milk production>focusNextField', {
        parentIndex,
        fieldIndex,
        e,
      });
    }
  };

  return (
    <View style={styles.container}>
      {/* from milk production else number input, TODO: handle one min value */}
      <NumberFormInput
        label={`${i18n.t(
          'averageMilkProductionAnimalsInTank',
        )} (${weightUnit})`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={!isEditable}
        hasCommas
        required
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ANIMALS_IN_TANK_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][
            ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG
          ]
        }
        onChange={handleAverageMilkProductionAnimalsInTankChange}
        blurOnSubmit={true}
        selectTextOnFocus={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        reference={reference => (inputRefs.current[0] = reference)}
        onSubmitEditing={() => inputRefs.current[1].focus()}
      />
      {/* from site setup */}

      <NumberFormInput
        label={`${i18n.t('averageMilkProductionLitresPerCowPerDay')}`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={true}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.MILK_PRODUCTION_KG]
        }
        blurOnSubmit={true}
        selectTextOnFocus={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
      />
      <NumberFormInput
        label={`${i18n.t('kgOfQuotaPerDay').replaceAll('kg', weightUnit)}`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={!isEditable}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.KG_OF_QUOTA_PER_DAY]
        }
        onChange={v => {
          setFieldValue(
            `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.KG_OF_QUOTA_PER_DAY}`,
            v,
          );
          handleTotalQuotaKgPerDayCalculation(
            v,
            values[ROF_FIELDS.MILK_PRODUCTION][
              ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY
            ],
          );
        }}
        blurOnSubmit={true}
        selectTextOnFocus={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        reference={reference => (inputRefs.current[1] = reference)}
        onSubmitEditing={() => inputRefs.current[2].focus()}
      />
      <NumberFormInput
        label={`${i18n
          .t('incentiveDaysKgPerDay')
          .replaceAll('kg', weightUnit)}`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={!isEditable}
        isInteger
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][
            ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY
          ]
        }
        onChange={v => {
          setFieldValue(
            `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY}`,
            v,
          );
          handleTotalQuotaKgPerDayCalculation(
            values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.KG_OF_QUOTA_PER_DAY],
            v,
          );
        }}
        blurOnSubmit={true}
        selectTextOnFocus={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        reference={reference => (inputRefs.current[2] = reference)}
        onSubmitEditing={() => inputRefs.current[0][0].focus()}
      />
      <NumberFormInput
        label={`${i18n.t('totalQuota')} (${weightUnit}${i18n.t('perDay')})`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={true}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY]
        }
        blurOnSubmit={true}
        selectTextOnFocus={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
      />
      <NumberFormInput
        label={`${i18n
          .t('currentQuotaUtilizationKgPerDay')
          .replace('kg', weightUnit)}`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={true}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][
            ROF_FIELDS.CURRENT_QUOTA_UTILIZATION_KG_PER_DAY
          ]
        }
        blurOnSubmit={true}
        selectTextOnFocus={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
      />
      {Object.values(ROF_MILKING_INGREDIENTS_TYPES).map(
        (milkingType, index) => {
          return (
            <MilkingIngredients
              milkingType={milkingType}
              isEditable={isEditable}
              setInputRef={setInputRef}
              focusNextField={focusNextField}
              index={index}
            />
          );
        },
      )}
    </View>
  );
};

export default MilkProduction;
