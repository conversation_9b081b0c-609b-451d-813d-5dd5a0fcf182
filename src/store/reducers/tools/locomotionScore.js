import LOCOMOTION_ACTIONS from '../../../constants/actionConstants/tools/locomotionScore';

const initialState = {
  // Initialize locomotion tool state
  locomotionToolData: null,

  selectedPen: null,

  loadingHerdData: false,
  herdData: null,
};

const locomotionScore = (state = initialState, action) => {
  switch (action.type) {
    //#region INITIALIZE_LOCOMOTION_SCORE_TOOL
    case LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_SUCCESS:
      return {
        ...state,
        locomotionToolData: action.payload,
      };

    case LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_FAILURE:
      return state;
    //#end region

    // # region selected pen changes
    case LOCOMOTION_ACTIONS.SET_SELECTED_LOCOMOTION_PEN_REQUEST:
      return {
        ...state,
        selectedPen: action.payload,
      };

    case LOCOMOTION_ACTIONS.UPDATE_SELECTED_PEN_DATA_REQUEST:
      return {
        ...state,
        selectedPen: {
          ...state.selectedPen,
          categories: action.payload,
        },
      };

    case LOCOMOTION_ACTIONS.UPDATE_SELECTED_PEN_FORM_DATA:
      return {
        ...state,
        selectedPen: action.payload,
      };

    case LOCOMOTION_ACTIONS.UPDATE_LOCOMOTION_TOOL_DATA_REQUEST:
      return {
        ...state,
        locomotionToolData: action.payload,
      };

    // #region update herd analysis goal data
    case LOCOMOTION_ACTIONS.UPDATE_LOCOMOTION_HERD_DATA:
      return {
        ...state,
        herdData: action.payload,
      };

    // #region initializing herd analysis data
    case LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_REQUEST:
      return {
        ...state,
        loadingHerdData: true,
      };
    case LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_SUCCESS:
      return {
        ...state,
        loadingHerdData: false,
        herdData: action.payload,
      };
    case LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_FAILURE:
      return {
        ...state,
        loadingHerdData: false,
      };
    // #end region

    // #region update locomotion state reducer with updated herd data reducer
    case LOCOMOTION_ACTIONS.UPDATE_HERD_REDUCER_TO_LOCOMOTION_REDUCER_REQUEST:
      return {
        ...state,
        locomotionToolData: {
          ...state.locomotionToolData,
          herd: action.payload,
        },
      };
    // #end region

    // #region clear reducer data
    case LOCOMOTION_ACTIONS.CLEAR_LOCOMOTION_REDUCER_REQUEST:
      return {
        ...state,
        ...initialState,
      };
    // #end region

    default:
      return state;
  }
};

export default locomotionScore;
