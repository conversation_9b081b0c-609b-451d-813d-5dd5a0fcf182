// modules
import { call, put, select, takeLatest } from 'redux-saga/effects';

// constants
import { VISIT_TABLE_FIELDS } from '../../../constants/AppConstants';
import ANIMAL_ANALYSIS_ACTIONS from '../../../constants/actionConstants/tools/animalAnalysis';

// actions
import {
  getAnimalsFailure,
  getAnimalsSuccess,
  createAnimalSuccess,
  createAnimalFailure,
  updateAnimalSuccess,
  updateAnimalFailure,
  deleteAnimalSuccess,
  deleteAnimalFailure,
  clearAnimalAnalysisFailure,
  clearAnimalAnalysisSuccess,
} from '../../actions/tools/animalAnalysis';
import { getBCSPenAnalysisRequest } from '../../actions/tools/bcs';

// data managers
import {
  createOfflineAnimal,
  deleteOfflineAnimal,
  getAnimalsByVisit,
  resetAnimalAnalysis,
  updateOfflineAnimal,
} from '../../../database/dataManager/VisitManager';

// helpers
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';
import { extendsUsedPensInVisitWithToolPens } from '../../../helpers/visitHelper';
import { extractUsedPensFromAnimalAnalysisTool } from '../../../helpers/animalHelper';

// sagas
import { setLocomotionPenAnalysisFromAnimalAnalysisSaga } from './locomotionScore';

const getSelectedVisit = state => state.visit.visit;
const allEnumsSelector = state => state.enums;

function* getAnimalsSaga(action) {
  try {
    let params = action.payload;
    let data = yield call(getAnimalsByVisit, params);
    yield put(getAnimalsSuccess({ data: data }));
  } catch (error) {
    yield put(getAnimalsFailure({ error: error.message }));
  }
}

function* clearAnimalAnalysisSaga(action) {
  try {
    let params = action.payload;
    let response = yield call(resetAnimalAnalysis, params);
    if (response) {
      yield put(clearAnimalAnalysisSuccess({}));
    }
  } catch (error) {
    yield put(clearAnimalAnalysisFailure({ error: error.message }));
  }
}

function* createAnimalSaga(action) {
  try {
    let model = action.payload;
    const visit = yield select(getSelectedVisit);

    const animalAnalysisUsedPens = yield call(
      extractUsedPensFromAnimalAnalysisTool,
      model,
      visit,
    );

    if (animalAnalysisUsedPens) {
      const updatedVisitUsedPens = yield call(
        extendsUsedPensInVisitWithToolPens,
        visit,
        animalAnalysisUsedPens,
      );

      updatedVisitUsedPens ? (model.usedPens = updatedVisitUsedPens) : null;
    }

    const response = yield call(createOfflineAnimal, model);

    /**
     * @summary
     * updating locomotion pen analysis from here because of concurrency to update database in a sequence
     * @example first update database with create animal and on success create and update locomotion pen analysis inside DB.
     * doing it for updating usedPens keys inside database.
     * adding locomotion function to update it's pen analysis if animal analysis has locomotion field
     */
    yield call(setLocomotionPenAnalysisFromAnimalAnalysisSaga, model);

    /**
     * @description
     * adding similar condition as above for locomotion but explicity defining here.
     * checking if animal analysis has BCS selected than we will add BCS pen analysis data in local database
     * @summary
     * updating BCS pen analysis from here because of concurrency to update database in a sequence
     */
    if (
      model?.animal?.bcsCategory ||
      !stringIsEmpty(model?.animal?.bcsCategory) ||
      model?.toolType == VISIT_TABLE_FIELDS.BODY_CONDITION
    ) {
      const enumState = yield select(allEnumsSelector);
      yield put(
        getBCSPenAnalysisRequest({
          localId: visit?.id,
          enumState: enumState,
          isEditable: visit?.isEditable,
        }),
      );
    }

    if (response) {
      yield put(createAnimalSuccess({}));
    }
  } catch (error) {
    yield put(createAnimalFailure({ error: error.message }));
  }
}

function* updateAnimalSaga(action) {
  try {
    let model = action.payload;
    let response = yield call(updateOfflineAnimal, model);
    if (response) {
      yield put(updateAnimalSuccess({}));
    }
  } catch (error) {
    yield put(updateAnimalFailure({ error: error.message }));
  }
}

function* deleteAnimalSaga(action) {
  try {
    let model = action.payload;
    let response = yield call(deleteOfflineAnimal, model);
    if (response) {
      yield put(deleteAnimalSuccess({}));
    }
  } catch (error) {
    yield put(deleteAnimalFailure({ error: error.message }));
  }
}

function* animalAnalysisSaga() {
  yield takeLatest(ANIMAL_ANALYSIS_ACTIONS.GET_ANIMALS_REQUEST, getAnimalsSaga);

  yield takeLatest(
    ANIMAL_ANALYSIS_ACTIONS.CLEAR_ANIMAL_ANALYSIS_REQUEST,
    clearAnimalAnalysisSaga,
  );

  yield takeLatest(
    ANIMAL_ANALYSIS_ACTIONS.CREATE_ANIMAL_REQUEST,
    createAnimalSaga,
  );

  yield takeLatest(
    ANIMAL_ANALYSIS_ACTIONS.UPDATE_ANIMAL_REQUEST,
    updateAnimalSaga,
  );

  yield takeLatest(
    ANIMAL_ANALYSIS_ACTIONS.DELETE_ANIMAL_REQUEST,
    deleteAnimalSaga,
  );
}

export default animalAnalysisSaga;
