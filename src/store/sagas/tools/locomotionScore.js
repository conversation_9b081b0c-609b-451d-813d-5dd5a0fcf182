// modules
import { call, delay, put, select, takeLatest } from 'redux-saga/effects';

// constants
import {
  VISIT_STATUS,
  VISIT_TABLE_FIELDS,
} from '../../../constants/AppConstants';
import LOCOMOTION_ACTIONS from '../../../constants/actionConstants/tools/locomotionScore';

// helpers
import {
  isLocomotionPenUsed,
  updateLocomotionHerdModel,
  updateLocomotionPensModel,
  initLocomotionSelectedPen,
  getPensWithAnimalObserved,
  initializeNewLocomotionPen,
  initializeLocomotionHerdData,
  initializeLocomotionToolData,
  extractUsedPensFromLocomotionScoreTool,
  replaceAnimalAnalysisPenInLocomotionPens,
} from '../../../helpers/locomotionHelper';
import { logEvent } from '../../../helpers/logHelper';
import { dateHelper } from '../../../helpers/dateHelper';
import { getParsedToolData } from '../../../helpers/genericHelper';
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';
import { extendsUsedPensInVisitWithToolPens } from '../../../helpers/visitHelper';

// data manager
import { saveLocomotionPenAnalysisByVisit } from '../../../database/dataManager/VisitManager';

// actions
import {
  clearLocomotionReducerRequest,
  setSelectedLocomotionPenRequest,
  updateLocomotionToolDataRequest,
  saveLocomotionPenAnalysisRequest,
  initializeLocomotionScoreToolSuccess,
  initializeLocomotionScoreToolFailure,
  initializeLocomotionHerdAnalysisSuccess,
  initializeLocomotionHerdAnalysisFailure,
  updateToolHerdToLocomotionReducerRequest,
} from '../../actions/tools/locomotionScore';
import { updateVisitInProgressStatus } from '../../actions/visit';

// services
import { _navigator } from '../../../services/navigationService';

// selectors
const pensListSelector = state => state.tool.pensList;
const selectedVisitSelector = state => state.visit.visit;
const currentSiteSelector = state => state.site.visitSite;
const herdDataSelector = state => state.locomotionScore.herdData;
const selectedPenSelector = state => state.locomotionScore.selectedPen;
const animalAnalysisSelector = state => state.visit.visit?.animalAnalysis;
const locomotionToolDataSelector = state =>
  state.locomotionScore.locomotionToolData;

function* saveLocomotionPenAnalysisSaga(action) {
  try {
    let model = action.payload;
    const locomotionUsedPens = yield call(
      extractUsedPensFromLocomotionScoreTool,
      model?.locomotionScoreData,
    );

    if (locomotionUsedPens) {
      const visit = yield select(selectedVisitSelector);

      const updatedVisitUsedPens = yield call(
        extendsUsedPensInVisitWithToolPens,
        visit,
        locomotionUsedPens,
      );

      updatedVisitUsedPens ? (model.usedPens = updatedVisitUsedPens) : null;
    }

    let response = yield call(saveLocomotionPenAnalysisByVisit, model);
    if (response) {
      yield put(updateVisitInProgressStatus());
    }
  } catch (error) {
    logEvent(
      'sagas -> locomotionScoreSaga -> saveLocomotionPenAnalysisSaga Error:',
      error,
    );
  }
}

function* initializeLocomotionToolSaga() {
  try {
    const site = yield select(currentSiteSelector);
    const pensList = yield select(pensListSelector);
    const visit = yield select(selectedVisitSelector);
    const animalAnalysis = yield select(animalAnalysisSelector);

    const parsedLocomotionData = yield call(
      getParsedToolData,
      visit?.locomotionScore,
    );

    // loading already saved locomotion tool data
    if (parsedLocomotionData && Object.keys(parsedLocomotionData)?.length > 0) {
      yield put(initializeLocomotionScoreToolSuccess(parsedLocomotionData));
      return;
    }

    // Initialize locomotion tool data using the helper function
    // The function now generates static model data
    const initializedData = yield call(
      initializeLocomotionToolData,
      pensList,
      animalAnalysis,
      site,
    );

    yield put(initializeLocomotionScoreToolSuccess(initializedData));
  } catch (error) {
    yield put(initializeLocomotionScoreToolFailure({ error: error.message }));
    logEvent(
      'sagas -> locomotionScoreSaga -> initializeLocomotionToolSaga Error:',
      error,
    );
  }
}

function* changeLocomotionPenSaga(action) {
  try {
    const pen = action.payload;

    const animalAnalysis = yield select(animalAnalysisSelector);
    const locomotionData = yield select(locomotionToolDataSelector);

    const isPenUsed = yield call(isLocomotionPenUsed, pen, locomotionData);
    if (isPenUsed) {
      yield put(setSelectedLocomotionPenRequest(isPenUsed));
      return;
    }

    const newSelectedPen = yield call(
      initializeNewLocomotionPen,
      pen,
      animalAnalysis,
    );
    yield put(setSelectedLocomotionPenRequest(newSelectedPen));
  } catch (error) {
    logEvent(
      'sagas -> locomotionScoreSaga -> changeLocomotionPenSaga Error:',
      error,
    );
  }
}

// this saga runs when we add animals in animal analysis
function* updateAnimalsObserveCountSaga(
  animalsObservedPen,
  shouldClearLocomotionReducerState = false,
) {
  try {
    let locomotionData = yield select(locomotionToolDataSelector);
    if (!locomotionData || Object.keys(locomotionData)?.length <= 0) {
      yield call(initializeLocomotionToolSaga);
      locomotionData = yield select(locomotionToolDataSelector);
    }

    const updatedLocomotionData = yield call(
      replaceAnimalAnalysisPenInLocomotionPens,
      animalsObservedPen,
      locomotionData,
    );

    yield put(updateLocomotionToolDataRequest(updatedLocomotionData));

    const visit = yield select(selectedVisitSelector);
    yield put(
      saveLocomotionPenAnalysisRequest({
        locomotionScoreData: updatedLocomotionData,
        localVisitId: visit?.id,
        updated_at: dateHelper.getUnixTimestamp(new Date()),
        mobileLastUpdatedTime: dateHelper.getUnixTimestamp(new Date()),
      }),
    );

    if (shouldClearLocomotionReducerState) {
      // clearing locomotion reducer data when unmounting with added delay
      yield delay(1000);
      yield put(clearLocomotionReducerRequest());
    }
  } catch (error) {
    logEvent(
      'sagas -> locomotionScoreSaga -> updateAnimalsObserveCountSaga Error:',
      error,
    );
  }
}

// #region initialize locomotion pen analysis data
function* initLocomotionPenRequestSaga() {
  try {
    const pensList = yield select(pensListSelector);
    const locomotionData = yield select(locomotionToolDataSelector);
    const animalAnalysis = yield select(animalAnalysisSelector);

    const selectedPen = yield call(
      initLocomotionSelectedPen,
      pensList,
      locomotionData,
      animalAnalysis,
    );

    yield put(setSelectedLocomotionPenRequest(selectedPen));
  } catch (error) {
    logEvent(
      'sagas -> locomotionScoreSaga -> initLocomotionPenRequestSaga Error:',
      error,
    );
  }
}

// #region initialize locomotion herd analysis data
function* initLocomotionHerdSaga() {
  try {
    const site = yield select(currentSiteSelector);
    const locomotionData = yield select(locomotionToolDataSelector);

    const herdData = yield call(
      initializeLocomotionHerdData,
      locomotionData,
      site,
    );

    yield delay(100);
    yield put(initializeLocomotionHerdAnalysisSuccess(herdData));
  } catch (error) {
    logEvent(
      'sagas -> locomotionScoreSaga -> initLocomotionHerdSaga Error:',
      error,
    );
    yield put(initializeLocomotionHerdAnalysisFailure(error));
  }
}

export function* setLocomotionPenAnalysisFromAnimalAnalysisSaga(animalData) {
  try {
    const routeService = yield call(_navigator.current?.getCurrentRoute);

    // check if locomotion field is selected when creating animal analysis
    if (
      !animalData?.animal?.locomotionScore ||
      stringIsEmpty(animalData?.animal?.locomotionScore)
    ) {
      return;
    }

    // create payload data for creating pen analysis inside locomotion tool
    const animalAnalysis = {
      animals: [
        {
          animalDetails: [animalData.animal],
          penId: animalData.animal.penId || animalData.animal.localPenId,
        },
      ],
      mobileLastUpdatedTime: animalData.mobileLastUpdatedTime,
    };

    const updatedPenWithAnimals = yield call(
      getPensWithAnimalObserved,
      animalAnalysis,
      [routeService?.params?.pen],
    );
    if (updatedPenWithAnimals?.length > 0) {
      /**
       * check if it needs to clear reducer of locomotion state.
       * it is necessary because when add animal from BCS we should clear locomotion reducer
       * else keep the locomotion state alive.
       */
      const shouldClearLocomotionReducerState =
        animalData?.toolType == VISIT_TABLE_FIELDS.BODY_CONDITION
          ? true
          : false;
      yield call(
        updateAnimalsObserveCountSaga,
        updatedPenWithAnimals,
        shouldClearLocomotionReducerState,
      );
    }
    return true;
  } catch (error) {
    logEvent(
      'sagas -> locomotionScoreSaga -> setLocomotionPenAnalysisFromAnimalAnalysisSaga Error:',
      error,
    );
  }
}

function* saveLocomotionDataOnUnmountSaga(action) {
  try {
    const payload = {};

    const visit = yield select(selectedVisitSelector);
    const toolHerdData = yield select(herdDataSelector);
    const selectedPen = yield select(selectedPenSelector);
    const locomotionState = yield select(locomotionToolDataSelector);

    /**
     * do not update data in local database if this visit is not editable. OR
     * this visit status is published
     */
    if (
      !visit?.isEditable ||
      [VISIT_STATUS.PUBLISHED].includes(visit?.visitStatus)
    ) {
      return;
    }

    const updatedLocomotionPens = yield call(
      updateLocomotionPensModel,
      locomotionState?.pens,
      selectedPen,
    );
    payload.pens = updatedLocomotionPens;

    if (toolHerdData) {
      const updatedHerdData = yield call(
        updateLocomotionHerdModel,
        toolHerdData,
        visit?.unitOfMeasure,
      );
      payload.herd = updatedHerdData;

      /**
       * @condition only runs this condition if locomotion state reducer does not have latest herd data.
       *
       * locomotion state reducer if it does not have herd data.
       * this case appears when user add pen analysis and comes to herd and update herd data.
       * so the locomotion reducer does not have herd data because herd data is stored in separate reducer state.
       * so update herd data reducer state and update it to locomotion tool state.
       */
      if (
        stringIsEmpty(locomotionState?.herd) ||
        Object.keys(locomotionState?.herd)?.length <= 0
      ) {
        yield put(updateToolHerdToLocomotionReducerRequest(toolHerdData));
      }
    }

    payload.localVisit = visit?.id;
    payload.visitId = visit?.sv_id;

    yield put(
      saveLocomotionPenAnalysisRequest({
        locomotionScoreData: payload,
        updated_at: dateHelper.getUnixTimestamp(new Date()),
        localVisitId: visit?.id,
        mobileLastUpdatedTime: dateHelper.getUnixTimestamp(new Date()),
      }),
    );

    if (action.payload?.clearLocomotionReducer) {
      // clearing locomotion reducer data when unmounting with added delay
      yield delay(1000);
      yield put(clearLocomotionReducerRequest());
    }
  } catch (error) {
    logEvent(
      'sagas -> locomotionScoreSaga -> saveLocomotionDataOnUnmountSaga Error:',
      error,
    );
  }
}

function* locomotionScoreSaga() {
  yield takeLatest(
    LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_REQUEST,
    initializeLocomotionToolSaga,
  );

  yield takeLatest(
    LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_ON_UNMOUNT_REQUEST,
    saveLocomotionDataOnUnmountSaga,
  );

  yield takeLatest(
    LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_REQUEST,
    saveLocomotionPenAnalysisSaga,
  );

  yield takeLatest(
    LOCOMOTION_ACTIONS.CHANGE_LOCOMOTION_PEN,
    changeLocomotionPenSaga,
  );

  yield takeLatest(
    LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SELECTED_PEN_REQUEST,
    initLocomotionPenRequestSaga,
  );

  yield takeLatest(
    LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_HERD_ANALYSIS_DATA_REQUEST,
    initLocomotionHerdSaga,
  );
}

export default locomotionScoreSaga;
