// modules
import React, { useCallback, useEffect } from 'react';
import { View, ScrollView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';

//styles
import styles from './styles';

// reusable components
import SyncContainer from '../../../components/common/SyncContainer';
import NoteBookActionsSection from '../../../components/Dashboard/NoteBookActionSection';
import PinnedContactsListSections from '../../../components/Dashboard/PinnedContactsListSection';
import RecentVisitListSection from '../../../components/Dashboard/RecentVisitListSection';
import DashboardTopBar from '../../../components/Dashboard/DashboardTopBar';
import { showAlertMsg } from '../../../components/common/Alerts';
import i18n from '../../../localization/i18n';
import { applyUpdate } from '../../../services/CodePushService';

//actions
import { getUnsyncedItemsCountRequest } from '../../../store/actions/dataSync';
import {
  getNotebookActionItemsRequest,
  getPinnedContactsRequest,
  getRecentInProgressVisitRequest,
  getSyncErrorsRequest,
  resetNavigateToNotebookRequest,
} from '../../../store/actions/dashboard';

//models
import withLogging from '../../../components/common/ScreenWithLogging';

let isAlertShown = false

const Dashboard = React.memo(() => {
  const dispatch = useDispatch();

  const isFocused = useIsFocused();

  const isSyncing = useSelector(state => state.dataSync?.isSyncing);
  const update = useSelector(state => state.versioning?.updateMeta);

  const getDashboardData = () => {
    dispatch(getPinnedContactsRequest());
    dispatch(getRecentInProgressVisitRequest());
    dispatch(getNotebookActionItemsRequest());
    dispatch(getSyncErrorsRequest());
  };

  useEffect(() => {
    if (isFocused) {
      getDashboardData();
      dispatch(resetNavigateToNotebookRequest());
    }
  }, [isFocused]);

  useEffect(() => {
    if (!isSyncing) {
      getDashboardData();
    }
  }, [isSyncing]);

  useFocusEffect(
    useCallback(() => {
      dispatch(getUnsyncedItemsCountRequest());
    }, [dispatch]),
  );

  const checkUpdate = useCallback(() => {
    isAlertShown = true;
    if (update && update?.isMandatory) {
        showAlertMsg(
            i18n.t('updateRequired'),
            i18n.t('updateRequiredDescription'),
            [
                {
                    text: i18n.t('ok'),
                    onPress: applyUpdate,
                },
            ],
            { cancelable: false },
        );
    }
  }, []);

  useEffect(() => {
    !isAlertShown && checkUpdate();
  }, [update?.isMandatory]);

  return (
    <>
      <DashboardTopBar />

      <ScrollView
        nestedScrollEnabled
        style={styles.scrollViewMargin}
        showsVerticalScrollIndicator={false}>
        <View style={styles.container}>
          <SyncContainer />

          <NoteBookActionsSection />

          <PinnedContactsListSections />

          <RecentVisitListSection />
        </View>
      </ScrollView>
    </>
  );
});

export default withLogging(Dashboard, 'Dashboard');
