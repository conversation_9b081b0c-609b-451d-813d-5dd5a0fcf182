import org.apache.tools.ant.taskdefs.condition.Os

// Top-level build file where you can add configuration options common to all sub-projects/modules.

gradle.startParameter.excludedTaskNames.addAll(
        gradle.startParameter.taskNames.findAll { it.contains("testClasses") }
)


buildscript {
    ext {
        googlePlayServicesVersion = "+" // default: "+"
        buildToolsVersion = "35.0.0"
        minSdkVersion = 30
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.25"
        androidXAnnotation = "1.8.1"
        androidXBrowser = "1.8.0"
    }
    repositories {
        google()
        mavenCentral()

        // ADD THIS
        maven { url "https://www.jitpack.io" }
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:1.5.0")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
        classpath("com.google.gms:google-services:4.4.2")
    }
}

apply plugin: "com.facebook.react.rootproject"
